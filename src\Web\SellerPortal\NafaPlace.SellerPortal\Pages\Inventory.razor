@page "/inventory"
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using NafaPlace.SellerPortal.Services
@using NafaPlace.SellerPortal.Models.Inventory
@attribute [Authorize]
@inject InventoryService InventoryService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService
@inject ILocalStorageService LocalStorage
@inject IAuthService AuthService
@inject ProductService ProductService

<link href="css/inventory.css" rel="stylesheet" />

<h1 class="visually-hidden">Gestion des Stocks - NafaPlace Vendeur</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Stocks</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Inventaire</li>
    </ol>

    @if (_isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        <!-- Statistiques du vendeur -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card inventory-stats-card bg-primary text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Mes Produits</h6>
                                <h3 class="mb-0">@_dashboard?.TotalProducts</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-box-seam fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-white stretched-link" href="/products">Voir les détails</a>
                        <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card inventory-stats-card bg-warning text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Stock Faible</h6>
                                <h3 class="mb-0">@_dashboard?.LowStockCount</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-exclamation-triangle fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <button class="btn btn-link text-white p-0 small" @onclick="() => ShowLowStockProducts()">
                            Voir les détails
                        </button>
                        <div class="small text-white"><i class="bi bi-exclamation-triangle"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card inventory-stats-card bg-danger text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Rupture de Stock</h6>
                                <h3 class="mb-0">@_dashboard?.OutOfStockProducts</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-x-circle fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <button class="btn btn-link text-white p-0 small" @onclick="() => ShowOutOfStockProducts()">
                            Voir les détails
                        </button>
                        <div class="small text-white"><i class="bi bi-x-circle"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card inventory-stats-card bg-success text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Valeur Stock</h6>
                                <h3 class="mb-0">@(_dashboard?.TotalInventoryValue.ToString("N0")) GNF</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-currency-exchange fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alertes récentes -->
        @if (_dashboard?.RecentAlerts?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-bell me-1"></i>
                        Mes Alertes (@_dashboard.RecentAlerts.Count(a => !a.IsAcknowledged))
                    </div>
                    <button class="btn btn-sm btn-outline-primary" @onclick="LoadDashboard">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Actualiser
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Type</th>
                                    <th>Stock Actuel</th>
                                    <th>Seuil</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var alert in _dashboard.RecentAlerts.Take(5))
                                {
                                    <tr class="@GetAlertRowClass(alert.Severity)">
                                        <td>@alert.ProductName</td>
                                        <td>
                                            <span class="badge @GetAlertBadgeClass(alert.Type)">
                                                @GetAlertTypeText(alert.Type)
                                            </span>
                                        </td>
                                        <td>@alert.CurrentStock</td>
                                        <td>@alert.ThresholdValue</td>
                                        <td>@alert.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                        <td>
                                            @if (!alert.IsAcknowledged)
                                            {
                                                <button class="btn btn-sm btn-outline-success" @onclick="() => AcknowledgeAlert(alert.Id)">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                            }
                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => NavigateToProduct(alert.ProductId)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }

        <!-- Produits en stock faible -->
        @if (_dashboard?.LowStockProducts?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    Produits Nécessitant un Réapprovisionnement
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Stock Actuel</th>
                                    <th>Quantité Vendue</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var product in _dashboard.LowStockProducts.Take(10))
                                {
                                    <tr class="table-warning">
                                        <td>
                                            <strong>@product.ProductName</strong>
                                            <br />
                                            <small class="text-muted">ID: @product.ProductId</small>
                                        </td>
                                        <td>
                                            <span class="text-warning fw-bold">@product.CurrentStock</span>
                                        </td>
                                        <td>@product.SoldQuantity</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary me-2" @onclick="() => ShowUpdateStockModal(product.ProductId, product.ProductName, product.CurrentStock)">
                                                <i class="bi bi-plus-circle me-1"></i>
                                                Réapprovisionner
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => NavigateToProduct(product.ProductId)">
                                                <i class="bi bi-pencil me-1"></i>
                                                Modifier
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }

        <!-- Historique des mouvements récents -->
        @if (_dashboard?.RecentMovements?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-clock-history me-1"></i>
                        Mouvements de Stock Récents
                    </div>
                    <button class="btn btn-sm btn-outline-primary" @onclick="ShowMovementsModal">
                        <i class="bi bi-list me-1"></i>
                        Voir tout l'historique
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Produit</th>
                                    <th>Type</th>
                                    <th>Quantité</th>
                                    <th>Stock Avant</th>
                                    <th>Stock Après</th>
                                    <th>Raison</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var movement in _dashboard.RecentMovements.Take(5))
                                {
                                    <tr>
                                        <td>@movement.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                        <td>@movement.ProductName</td>
                                        <td>
                                            <span class="badge @GetMovementBadgeClass(movement.Type)">
                                                @GetMovementTypeText(movement.Type)
                                            </span>
                                        </td>
                                        <td>
                                            <span class="@(movement.Type == MovementType.Sale || movement.Type == MovementType.Damage ? "text-danger" : "text-success")">
                                                @(movement.Type == MovementType.Sale || movement.Type == MovementType.Damage ? "-" : "+")@movement.Quantity
                                            </span>
                                        </td>
                                        <td>@movement.PreviousStock</td>
                                        <td>@movement.NewStock</td>
                                        <td>
                                            <small class="text-muted">@movement.Reason</small>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }

        <!-- Actions rapides -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-lightning me-1"></i>
                        Actions Rapides
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100 mb-2" @onclick="NavigateToProducts">
                                    <i class="bi bi-box me-1"></i>
                                    Gérer mes Produits
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success w-100 mb-2" @onclick="ShowBulkUpdateModal">
                                    <i class="bi bi-arrow-up-circle me-1"></i>
                                    Mise à Jour en Lot
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info w-100 mb-2" @onclick="NavigateToOrders">
                                    <i class="bi bi-cart me-1"></i>
                                    Voir les Commandes
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100 mb-2" @onclick="ShowExportModal">
                                    <i class="bi bi-download me-1"></i>
                                    Exporter les Données
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- Modal de mise à jour du stock -->
@if (_showUpdateStockModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Mettre à jour le stock</h5>
                    <button type="button" class="btn-close" @onclick="HideUpdateStockModal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Produit</label>
                        <input type="text" class="form-control" value="@_selectedProductName" readonly />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Stock actuel</label>
                        <input type="number" class="form-control" value="@_currentStock" readonly />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nouveau stock</label>
                        <input type="number" class="form-control" @bind="_newStock" min="0" />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Raison</label>
                        <select class="form-select" @bind="_updateReason">
                            <option value="">Sélectionner une raison</option>
                            <option value="Réapprovisionnement">Réapprovisionnement</option>
                            <option value="Correction d'inventaire">Correction d'inventaire</option>
                            <option value="Retour client">Retour client</option>
                            <option value="Produit endommagé">Produit endommagé</option>
                            <option value="Autre">Autre</option>
                        </select>
                    </div>
                    @if (_updateReason == "Autre")
                    {
                        <div class="mb-3">
                            <label class="form-label">Préciser</label>
                            <input type="text" class="form-control" @bind="_customReason" placeholder="Préciser la raison..." />
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="HideUpdateStockModal">Annuler</button>
                    <button type="button" class="btn btn-primary" @onclick="UpdateStock" disabled="@(_isUpdatingStock || string.IsNullOrEmpty(_updateReason))">
                        @if (_isUpdatingStock)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        Mettre à jour
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Modal de mise à jour en lot -->
@if (_showBulkUpdateModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-arrow-up-circle me-2"></i>
                        Mise à Jour en Lot des Stocks
                    </h5>
                    <button type="button" class="btn-close" @onclick="HideBulkUpdateModal"></button>
                </div>
                <div class="modal-body">
                    @if (_isLoadingProducts)
                    {
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement des produits...</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- Filtres et recherche -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Rechercher un produit</label>
                                <input type="text" class="form-control" placeholder="Nom du produit..."
                                       @onchange="OnSearchChanged" value="@_searchTerm" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Filtrer par stock</label>
                                <select class="form-select" @onchange="OnStockFilterChanged" value="@_stockFilter">
                                    <option value="all">Tous les produits</option>
                                    <option value="low">Stock faible (≤10)</option>
                                    <option value="out">Rupture de stock</option>
                                    <option value="normal">Stock normal (>10)</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Actions rapides</label>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" @onclick="() => ApplyStockToSelected(0)">
                                        0 Stock
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" @onclick="() => ApplyStockToSelected(10)">
                                        10 Stock
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" @onclick="() => ApplyStockToSelected(50)">
                                        50 Stock
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Sélection</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked="@_selectAll" @onchange="ToggleSelectAll" />
                                    <label class="form-check-label">Tout sélectionner</label>
                                </div>
                            </div>
                        </div>

                        <!-- Statistiques de sélection -->
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>@GetSelectedCount()</strong> produit(s) sélectionné(s) -
                            <strong>@GetModifiedCount()</strong> avec modifications
                        </div>

                        <!-- Liste des produits -->
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-sm table-hover">
                                <thead class="table-light sticky-top">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input" checked="@_selectAll" @onchange="ToggleSelectAll" />
                                        </th>
                                        <th>Produit</th>
                                        <th width="100">Stock Actuel</th>
                                        <th width="120">Nouveau Stock</th>
                                        <th width="150">Raison Spécifique</th>
                                        <th width="80">Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var product in _filteredProducts)
                                    {
                                        <tr class="@(product.IsSelected ? "table-primary" : "")">
                                            <td>
                                                <input type="checkbox" class="form-check-input"
                                                       checked="@product.IsSelected"
                                                       @onchange="() => ToggleProductSelection(product)" />
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(product.ImageUrl))
                                                    {
                                                        <img src="@product.ImageUrl" alt="@product.Name"
                                                             class="me-2" style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;" />
                                                    }
                                                    <div>
                                                        <div class="fw-bold">@product.Name</div>
                                                        <small class="text-muted">ID: @product.Id</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge @(product.CurrentStock == 0 ? "bg-danger" : product.CurrentStock <= 10 ? "bg-warning" : "bg-success")">
                                                    @product.CurrentStock
                                                </span>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control form-control-sm"
                                                       min="0" value="@product.NewStock"
                                                       @onchange="(e) => UpdateNewStock(product, e)"
                                                       disabled="@(!product.IsSelected)" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control form-control-sm"
                                                       placeholder="Optionnel..."
                                                       @bind="product.SpecificReason"
                                                       disabled="@(!product.IsSelected)" />
                                            </td>
                                            <td>
                                                @if (product.NewStock != product.CurrentStock && product.IsSelected)
                                                {
                                                    <i class="bi bi-pencil-square text-warning" title="Modifié"></i>
                                                }
                                                else if (product.IsSelected)
                                                {
                                                    <i class="bi bi-check-circle text-success" title="Sélectionné"></i>
                                                }
                                                else
                                                {
                                                    <i class="bi bi-circle text-muted" title="Non sélectionné"></i>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (GetTotalPages() > 1)
                        {
                            <nav aria-label="Pagination des produits">
                                <ul class="pagination pagination-sm justify-content-center mt-3">
                                    <li class="page-item @(_currentPage == 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(_currentPage - 1)">Précédent</button>
                                    </li>
                                    @for (int i = Math.Max(1, _currentPage - 2); i <= Math.Min(GetTotalPages(), _currentPage + 2); i++)
                                    {
                                        <li class="page-item @(_currentPage == i ? "active" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                        </li>
                                    }
                                    <li class="page-item @(_currentPage == GetTotalPages() ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(_currentPage + 1)">Suivant</button>
                                    </li>
                                </ul>
                            </nav>
                        }

                        <!-- Raison de la mise à jour -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <label class="form-label">Raison de la mise à jour *</label>
                                <select class="form-select" @bind="_bulkUpdateReason">
                                    <option value="">Sélectionner une raison</option>
                                    <option value="Réapprovisionnement en lot">Réapprovisionnement en lot</option>
                                    <option value="Correction d'inventaire">Correction d'inventaire</option>
                                    <option value="Ajustement saisonnier">Ajustement saisonnier</option>
                                    <option value="Promotion/Liquidation">Promotion/Liquidation</option>
                                    <option value="Retour fournisseur">Retour fournisseur</option>
                                    <option value="Autre">Autre</option>
                                </select>
                            </div>
                            @if (_bulkUpdateReason == "Autre")
                            {
                                <div class="col-md-6">
                                    <label class="form-label">Préciser la raison *</label>
                                    <input type="text" class="form-control" @bind="_bulkCustomReason"
                                           placeholder="Préciser la raison de la mise à jour..." />
                                </div>
                            }
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="HideBulkUpdateModal">Annuler</button>
                    <button type="button" class="btn btn-primary" @onclick="ExecuteBulkUpdate"
                            disabled="@(_isBulkUpdating || GetModifiedCount() == 0 || string.IsNullOrEmpty(_bulkUpdateReason) || (_bulkUpdateReason == "Autre" && string.IsNullOrEmpty(_bulkCustomReason)))">
                        @if (_isBulkUpdating)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        <i class="bi bi-check-circle me-1"></i>
                        Mettre à jour (@GetModifiedCount() produit(s))
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Modal de l'historique des mouvements -->
@if (_showMovementsModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock-history me-2"></i>
                        Historique des Mouvements de Stock
                    </h5>
                    <button type="button" class="btn-close" @onclick="HideMovementsModal"></button>
                </div>
                <div class="modal-body">
                    @if (_isLoadingMovements)
                    {
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement de l'historique...</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- Filtres pour l'historique -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Type de mouvement</label>
                                <select class="form-select" @onchange="OnMovementTypeFilterChanged" value="@_movementTypeFilter">
                                    <option value="all">Tous les types</option>
                                    <option value="1">Réapprovisionnement</option>
                                    <option value="2">Vente</option>
                                    <option value="3">Retour</option>
                                    <option value="4">Ajustement</option>
                                    <option value="6">Dommage/Perte</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Période</label>
                                <select class="form-select" @onchange="OnPeriodFilterChanged" value="@_periodFilter">
                                    <option value="7">7 derniers jours</option>
                                    <option value="30">30 derniers jours</option>
                                    <option value="90">3 derniers mois</option>
                                    <option value="365">Dernière année</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Rechercher un produit</label>
                                <input type="text" class="form-control" placeholder="Nom du produit..."
                                       @onchange="OnMovementSearchChanged" value="@_movementSearchTerm" />
                            </div>
                        </div>

                        <!-- Tableau des mouvements -->
                        <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                            <table class="table table-sm table-hover">
                                <thead class="table-light sticky-top">
                                    <tr>
                                        <th>Date</th>
                                        <th>Produit</th>
                                        <th>Type</th>
                                        <th>Quantité</th>
                                        <th>Stock Avant</th>
                                        <th>Stock Après</th>
                                        <th>Raison</th>
                                        <th>Utilisateur</th>
                                        <th>Référence</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var movement in _filteredMovements)
                                    {
                                        <tr>
                                            <td>@movement.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>
                                                <strong>@movement.ProductName</strong>
                                                <br />
                                                <small class="text-muted">ID: @movement.ProductId</small>
                                            </td>
                                            <td>
                                                <span class="badge @GetMovementBadgeClass(movement.Type)">
                                                    @GetMovementTypeText(movement.Type)
                                                </span>
                                            </td>
                                            <td>
                                                <span class="@(movement.Type == MovementType.Sale || movement.Type == MovementType.Damage ? "text-danger fw-bold" : "text-success fw-bold")">
                                                    @(movement.Type == MovementType.Sale || movement.Type == MovementType.Damage ? "-" : "+")@movement.Quantity
                                                </span>
                                            </td>
                                            <td>@movement.PreviousStock</td>
                                            <td>@movement.NewStock</td>
                                            <td>
                                                <small>@movement.Reason</small>
                                                @if (!string.IsNullOrEmpty(movement.Notes))
                                                {
                                                    <br />
                                                    <small class="text-muted fst-italic">@movement.Notes</small>
                                                }
                                            </td>
                                            <td>
                                                <small>@movement.UserName</small>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(movement.Reference))
                                                {
                                                    <small class="text-primary">@movement.Reference</small>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination pour les mouvements -->
                        @if (GetMovementsTotalPages() > 1)
                        {
                            <nav aria-label="Pagination des mouvements">
                                <ul class="pagination pagination-sm justify-content-center mt-3">
                                    <li class="page-item @(_movementsCurrentPage == 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangeMovementsPage(_movementsCurrentPage - 1)">Précédent</button>
                                    </li>
                                    @for (int i = Math.Max(1, _movementsCurrentPage - 2); i <= Math.Min(GetMovementsTotalPages(), _movementsCurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(_movementsCurrentPage == i ? "active" : "")">
                                            <button class="page-link" @onclick="() => ChangeMovementsPage(i)">@i</button>
                                        </li>
                                    }
                                    <li class="page-item @(_movementsCurrentPage == GetMovementsTotalPages() ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangeMovementsPage(_movementsCurrentPage + 1)">Suivant</button>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="HideMovementsModal">Fermer</button>
                    <button type="button" class="btn btn-primary" @onclick="ExportMovements">
                        <i class="bi bi-download me-1"></i>
                        Exporter CSV
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Modal d'export des données -->
@if (_showExportModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-download me-2"></i>
                        Exporter les Données d'Inventaire
                    </h5>
                    <button type="button" class="btn-close" @onclick="HideExportModal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Type de données à exporter</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="_exportProducts" id="exportProducts">
                            <label class="form-check-label" for="exportProducts">
                                Liste des produits avec stocks
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="_exportMovements" id="exportMovements">
                            <label class="form-check-label" for="exportMovements">
                                Historique des mouvements
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="_exportAlerts" id="exportAlerts">
                            <label class="form-check-label" for="exportAlerts">
                                Alertes de stock
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Période pour l'historique</label>
                        <select class="form-select" @bind="_exportPeriod">
                            <option value="7">7 derniers jours</option>
                            <option value="30">30 derniers jours</option>
                            <option value="90">3 derniers mois</option>
                            <option value="365">Dernière année</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Format d'export</label>
                        <select class="form-select" @bind="_exportFormat">
                            <option value="csv">CSV (Excel)</option>
                            <option value="json">JSON</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="HideExportModal">Annuler</button>
                    <button type="button" class="btn btn-primary" @onclick="ExecuteExport"
                            disabled="@(_isExporting || (!_exportProducts && !_exportMovements && !_exportAlerts))">
                        @if (_isExporting)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        <i class="bi bi-download me-1"></i>
                        Exporter
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private InventoryDashboardDto? _dashboard;
    private bool _isLoading = true;
    private int _sellerId;

    // Modal de mise à jour du stock
    private bool _showUpdateStockModal = false;
    private bool _isUpdatingStock = false;
    private int _selectedProductId;
    private string _selectedProductName = "";
    private int _currentStock;
    private int _newStock;
    private string _updateReason = "";
    private string _customReason = "";

    // Modal de mise à jour en lot
    private bool _showBulkUpdateModal = false;
    private bool _isLoadingProducts = false;
    private bool _isBulkUpdating = false;
    private List<ProductInfo> _sellerProducts = new();
    private List<ProductInfo> _filteredProducts = new();
    private string _bulkUpdateReason = "";
    private string _bulkCustomReason = "";
    private string _searchTerm = "";
    private string _stockFilter = "all"; // all, low, out, normal
    private bool _selectAll = false;
    private int _currentPage = 1;
    private int _pageSize = 20;
    private int _totalProducts = 0;

    // Modal de l'historique des mouvements
    private bool _showMovementsModal = false;
    private bool _isLoadingMovements = false;
    private List<StockMovementDto> _allMovements = new();
    private List<StockMovementDto> _filteredMovements = new();
    private string _movementTypeFilter = "all";
    private string _periodFilter = "30";
    private string _movementSearchTerm = "";
    private int _movementsCurrentPage = 1;
    private int _movementsPageSize = 20;
    private int _totalMovements = 0;

    // Modal d'export
    private bool _showExportModal = false;
    private bool _isExporting = false;
    private bool _exportProducts = true;
    private bool _exportMovements = false;
    private bool _exportAlerts = false;
    private string _exportPeriod = "30";
    private string _exportFormat = "csv";

    protected override async Task OnInitializedAsync()
    {
        await LoadSellerInfo();
        await LoadDashboard();
    }

    private async Task LoadSellerInfo()
    {
        try
        {
            var currentUser = await AuthService.GetCurrentUserAsync();
            if (currentUser.IsAuthenticated && currentUser.Id > 0)
            {
                // Récupérer l'ID du vendeur basé sur l'ID utilisateur
                var sellerInfo = await ProductService.GetSellerByUserIdAsync(currentUser.Id);
                if (sellerInfo != null)
                {
                    _sellerId = sellerInfo.Id;
                    Console.WriteLine($"Vendeur connecté: ID={_sellerId}, Nom={sellerInfo.Name}");
                }
                else
                {
                    Console.WriteLine($"Aucun vendeur trouvé pour l'utilisateur {currentUser.Id}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des informations du vendeur: {ex.Message}");
            NotificationService.Error($"Erreur lors de la récupération des informations du vendeur: {ex.Message}");
        }
    }

    private async Task LoadDashboard()
    {
        _isLoading = true;
        try
        {
            // Utiliser le sellerId spécifique pour récupérer uniquement les données du vendeur connecté
            if (_sellerId > 0)
            {
                _dashboard = await InventoryService.GetInventoryDashboardAsync(_sellerId);
            }
            else
            {
                NotificationService.Warning("Impossible de charger les données d'inventaire. Vendeur non identifié.");
                _dashboard = new InventoryDashboardDto();
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement du tableau de bord: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task AcknowledgeAlert(int alertId)
    {
        try
        {
            await InventoryService.AcknowledgeAlertAsync(alertId);
            await LoadDashboard();
            NotificationService.Success("Alerte acquittée avec succès");
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'acquittement: {ex.Message}");
        }
    }

    private void NavigateToProduct(int productId)
    {
        NavigationManager.NavigateTo($"/products/edit/{productId}");
    }

    private void ShowUpdateStockModal(int productId, string productName, int currentStock)
    {
        _selectedProductId = productId;
        _selectedProductName = productName;
        _currentStock = currentStock;
        _newStock = currentStock;
        _updateReason = "";
        _customReason = "";
        _showUpdateStockModal = true;
        StateHasChanged();
    }

    private void HideUpdateStockModal()
    {
        _showUpdateStockModal = false;
        StateHasChanged();
    }

    private async Task UpdateStock()
    {
        if (string.IsNullOrEmpty(_updateReason)) return;

        _isUpdatingStock = true;
        try
        {
            var reason = _updateReason == "Autre" ? _customReason : _updateReason;
            var request = new UpdateStockRequest
            {
                NewQuantity = _newStock,
                Reason = reason,
                Notes = $"Mise à jour depuis le portail vendeur - Stock précédent: {_currentStock}"
            };

            var success = await InventoryService.UpdateStockAsync(_selectedProductId, request);
            if (success)
            {
                HideUpdateStockModal();
                await LoadDashboard();
                NotificationService.Success("Stock mis à jour avec succès");
            }
            else
            {
                NotificationService.Error("Erreur lors de la mise à jour du stock");
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur: {ex.Message}");
        }
        finally
        {
            _isUpdatingStock = false;
            StateHasChanged();
        }
    }

    private async Task ShowBulkUpdateModal()
    {
        _showBulkUpdateModal = true;
        _searchTerm = "";
        _stockFilter = "all";
        _selectAll = false;
        _bulkUpdateReason = "";
        _bulkCustomReason = "";
        _currentPage = 1;

        await LoadSellerProducts();
        StateHasChanged();
    }

    private void HideBulkUpdateModal()
    {
        _showBulkUpdateModal = false;
        _sellerProducts.Clear();
        _filteredProducts.Clear();
        StateHasChanged();
    }

    private async Task LoadSellerProducts()
    {
        _isLoadingProducts = true;
        try
        {
            if (_sellerId > 0)
            {
                _sellerProducts = await InventoryService.GetSellerProductsAsync(_sellerId, 1, 100); // Charger plus de produits
                ApplyFilters();
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement des produits: {ex.Message}");
        }
        finally
        {
            _isLoadingProducts = false;
            StateHasChanged();
        }
    }

    private void ApplyFilters()
    {
        _filteredProducts = _sellerProducts.Where(p =>
        {
            // Filtre par terme de recherche
            bool matchesSearch = string.IsNullOrEmpty(_searchTerm) ||
                                p.Name.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase);

            // Filtre par statut de stock
            bool matchesStockFilter = _stockFilter switch
            {
                "low" => p.CurrentStock <= 10,
                "out" => p.CurrentStock == 0,
                "normal" => p.CurrentStock > 10,
                _ => true
            };

            return matchesSearch && matchesStockFilter;
        }).ToList();

        _totalProducts = _filteredProducts.Count;

        // Pagination
        _filteredProducts = _filteredProducts
            .Skip((_currentPage - 1) * _pageSize)
            .Take(_pageSize)
            .ToList();

        StateHasChanged();
    }

    private void OnSearchChanged(ChangeEventArgs e)
    {
        _searchTerm = e.Value?.ToString() ?? "";
        _currentPage = 1;
        ApplyFilters();
    }

    private void OnStockFilterChanged(ChangeEventArgs e)
    {
        _stockFilter = e.Value?.ToString() ?? "all";
        _currentPage = 1;
        ApplyFilters();
    }

    private void ToggleSelectAll(ChangeEventArgs e)
    {
        _selectAll = (bool)(e.Value ?? false);
        foreach (var product in _filteredProducts)
        {
            product.IsSelected = _selectAll;
        }
        StateHasChanged();
    }

    private void ToggleProductSelection(ProductInfo product)
    {
        product.IsSelected = !product.IsSelected;

        // Mettre à jour le statut "Tout sélectionner"
        _selectAll = _filteredProducts.All(p => p.IsSelected);
        StateHasChanged();
    }

    private void UpdateNewStock(ProductInfo product, ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var newStock))
        {
            product.NewStock = Math.Max(0, newStock);
        }
        StateHasChanged();
    }

    private void ApplyStockToSelected(int stockValue)
    {
        foreach (var product in _filteredProducts.Where(p => p.IsSelected))
        {
            product.NewStock = Math.Max(0, stockValue);
        }
        StateHasChanged();
    }

    private async Task ExecuteBulkUpdate()
    {
        var selectedProducts = _sellerProducts.Where(p => p.IsSelected && p.NewStock != p.CurrentStock).ToList();

        if (!selectedProducts.Any())
        {
            NotificationService.Warning("Aucun produit sélectionné avec des modifications de stock.");
            return;
        }

        if (string.IsNullOrEmpty(_bulkUpdateReason))
        {
            NotificationService.Warning("Veuillez sélectionner une raison pour la mise à jour.");
            return;
        }

        _isBulkUpdating = true;
        try
        {
            var reason = _bulkUpdateReason == "Autre" ? _bulkCustomReason : _bulkUpdateReason;
            var request = new BulkStockUpdateRequest
            {
                Reason = reason,
                Notes = $"Mise à jour en lot depuis le portail vendeur - {selectedProducts.Count} produit(s) modifié(s)",
                Items = selectedProducts.Select(p => new StockUpdateItem
                {
                    ProductId = p.Id,
                    NewQuantity = p.NewStock,
                    ProductSpecificReason = p.SpecificReason
                }).ToList()
            };

            var result = await InventoryService.BulkUpdateStockAsync(request);

            if (result.Success)
            {
                NotificationService.Success($"Mise à jour réussie: {result.SuccessfulUpdates} produit(s) mis à jour");
                HideBulkUpdateModal();
                await LoadDashboard();
            }
            else
            {
                var errorMessage = result.Errors.Any() ? string.Join(", ", result.Errors) : result.Message;
                NotificationService.Error($"Erreur lors de la mise à jour: {errorMessage}");
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur: {ex.Message}");
        }
        finally
        {
            _isBulkUpdating = false;
            StateHasChanged();
        }
    }

    private void ChangePage(int newPage)
    {
        if (newPage >= 1 && newPage <= GetTotalPages())
        {
            _currentPage = newPage;
            ApplyFilters();
        }
    }

    private int GetTotalPages()
    {
        return (int)Math.Ceiling((double)_totalProducts / _pageSize);
    }

    private int GetSelectedCount()
    {
        return _sellerProducts.Count(p => p.IsSelected);
    }

    private int GetModifiedCount()
    {
        return _sellerProducts.Count(p => p.IsSelected && p.NewStock != p.CurrentStock);
    }

    // Méthodes d'aide pour les styles
    private string GetAlertRowClass(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Critical => "table-danger",
            AlertSeverity.Warning => "table-warning",
            AlertSeverity.Emergency => "table-danger",
            _ => ""
        };
    }

    private string GetAlertBadgeClass(AlertType type)
    {
        return type switch
        {
            AlertType.OutOfStock => "bg-danger",
            AlertType.LowStock => "bg-warning",
            AlertType.OverStock => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetAlertTypeText(AlertType type)
    {
        return type switch
        {
            AlertType.OutOfStock => "Rupture",
            AlertType.LowStock => "Stock Faible",
            AlertType.OverStock => "Surstock",
            AlertType.StockMovement => "Mouvement",
            AlertType.ReservationExpiry => "Réservation",
            _ => type.ToString()
        };
    }

    private void NavigateToProducts()
    {
        NavigationManager.NavigateTo("/products");
    }

    private void NavigateToOrders()
    {
        NavigationManager.NavigateTo("/orders");
    }

    private void ShowLowStockProducts()
    {
        // Afficher une notification ou filtrer les produits en stock faible
        if (_dashboard?.LowStockCount > 0)
        {
            NotificationService.Info($"Il y a {_dashboard.LowStockCount} produit(s) en stock faible. Consultez la section 'Produits en Stock Faible' ci-dessous.");
        }
        else
        {
            NotificationService.Success("Aucun produit en stock faible actuellement.");
        }
    }

    private void ShowOutOfStockProducts()
    {
        // Afficher une notification ou filtrer les produits en rupture de stock
        if (_dashboard?.OutOfStockProducts > 0)
        {
            NotificationService.Warning($"Il y a {_dashboard.OutOfStockProducts} produit(s) en rupture de stock. Action requise!");
        }
        else
        {
            NotificationService.Success("Aucun produit en rupture de stock actuellement.");
        }
    }

    // Méthodes pour l'historique des mouvements
    private async Task ShowMovementsModal()
    {
        _showMovementsModal = true;
        _movementTypeFilter = "all";
        _periodFilter = "30";
        _movementSearchTerm = "";
        _movementsCurrentPage = 1;

        await LoadMovements();
        StateHasChanged();
    }

    private void HideMovementsModal()
    {
        _showMovementsModal = false;
        _allMovements.Clear();
        _filteredMovements.Clear();
        StateHasChanged();
    }

    private async Task LoadMovements()
    {
        _isLoadingMovements = true;
        try
        {
            if (_sellerId > 0)
            {
                // Charger plus de mouvements pour avoir un historique complet
                _allMovements = await InventoryService.GetSellerMovementsAsync(_sellerId, 1, 200);
                ApplyMovementFilters();
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement de l'historique: {ex.Message}");
        }
        finally
        {
            _isLoadingMovements = false;
            StateHasChanged();
        }
    }

    private void ApplyMovementFilters()
    {
        _filteredMovements = _allMovements.Where(m =>
        {
            // Filtre par type de mouvement
            bool matchesType = _movementTypeFilter == "all" ||
                              ((int)m.Type).ToString() == _movementTypeFilter;

            // Filtre par période
            var periodDays = int.Parse(_periodFilter);
            bool matchesPeriod = m.CreatedAt >= DateTime.UtcNow.AddDays(-periodDays);

            // Filtre par terme de recherche
            bool matchesSearch = string.IsNullOrEmpty(_movementSearchTerm) ||
                                m.ProductName.Contains(_movementSearchTerm, StringComparison.OrdinalIgnoreCase);

            return matchesType && matchesPeriod && matchesSearch;
        }).OrderByDescending(m => m.CreatedAt).ToList();

        _totalMovements = _filteredMovements.Count;

        // Pagination
        _filteredMovements = _filteredMovements
            .Skip((_movementsCurrentPage - 1) * _movementsPageSize)
            .Take(_movementsPageSize)
            .ToList();

        StateHasChanged();
    }

    private void OnMovementTypeFilterChanged(ChangeEventArgs e)
    {
        _movementTypeFilter = e.Value?.ToString() ?? "all";
        _movementsCurrentPage = 1;
        ApplyMovementFilters();
    }

    private void OnPeriodFilterChanged(ChangeEventArgs e)
    {
        _periodFilter = e.Value?.ToString() ?? "30";
        _movementsCurrentPage = 1;
        ApplyMovementFilters();
    }

    private void OnMovementSearchChanged(ChangeEventArgs e)
    {
        _movementSearchTerm = e.Value?.ToString() ?? "";
        _movementsCurrentPage = 1;
        ApplyMovementFilters();
    }

    private void ChangeMovementsPage(int newPage)
    {
        if (newPage >= 1 && newPage <= GetMovementsTotalPages())
        {
            _movementsCurrentPage = newPage;
            ApplyMovementFilters();
        }
    }

    private int GetMovementsTotalPages()
    {
        return (int)Math.Ceiling((double)_totalMovements / _movementsPageSize);
    }

    private string GetMovementBadgeClass(MovementType type)
    {
        return type switch
        {
            MovementType.Purchase => "bg-success",
            MovementType.Sale => "bg-primary",
            MovementType.Return => "bg-info",
            MovementType.Adjustment => "bg-warning",
            MovementType.Transfer => "bg-secondary",
            MovementType.Damage => "bg-danger",
            MovementType.Reservation => "bg-dark",
            MovementType.Release => "bg-light text-dark",
            _ => "bg-secondary"
        };
    }

    private string GetMovementTypeText(MovementType type)
    {
        return type switch
        {
            MovementType.Purchase => "Réapprovisionnement",
            MovementType.Sale => "Vente",
            MovementType.Return => "Retour",
            MovementType.Adjustment => "Ajustement",
            MovementType.Transfer => "Transfert",
            MovementType.Damage => "Dommage/Perte",
            MovementType.Reservation => "Réservation",
            MovementType.Release => "Libération",
            _ => type.ToString()
        };
    }

    // Méthodes pour l'export des données
    private void ShowExportModal()
    {
        _showExportModal = true;
        _exportProducts = true;
        _exportMovements = false;
        _exportAlerts = false;
        _exportPeriod = "30";
        _exportFormat = "csv";
        StateHasChanged();
    }

    private void HideExportModal()
    {
        _showExportModal = false;
        StateHasChanged();
    }

    private async Task ExecuteExport()
    {
        if (!_exportProducts && !_exportMovements && !_exportAlerts)
        {
            NotificationService.Warning("Veuillez sélectionner au moins un type de données à exporter.");
            return;
        }

        _isExporting = true;
        try
        {
            var exportData = new Dictionary<string, object>();

            if (_exportProducts)
            {
                var products = await InventoryService.GetSellerProductsAsync(_sellerId, 1, 1000);
                exportData["products"] = products.Select(p => new
                {
                    Id = p.Id,
                    Nom = p.Name,
                    Prix = p.Price,
                    StockActuel = p.CurrentStock,
                    Devise = p.Currency,
                    Vendeur = p.SellerName
                }).ToList();
            }

            if (_exportMovements)
            {
                var periodDays = int.Parse(_exportPeriod);
                var movements = await InventoryService.GetSellerMovementsAsync(_sellerId, 1, 1000);
                var filteredMovements = movements.Where(m => m.CreatedAt >= DateTime.UtcNow.AddDays(-periodDays)).ToList();

                exportData["movements"] = filteredMovements.Select(m => new
                {
                    Date = m.CreatedAt.ToString("dd/MM/yyyy HH:mm"),
                    Produit = m.ProductName,
                    Type = GetMovementTypeText(m.Type),
                    Quantite = m.Quantity,
                    StockAvant = m.PreviousStock,
                    StockApres = m.NewStock,
                    Raison = m.Reason,
                    Utilisateur = m.UserName,
                    Reference = m.Reference,
                    Notes = m.Notes
                }).ToList();
            }

            if (_exportAlerts)
            {
                var alerts = await InventoryService.GetSellerAlertsAsync(_sellerId);
                exportData["alerts"] = alerts.Select(a => new
                {
                    Date = a.CreatedAt.ToString("dd/MM/yyyy HH:mm"),
                    Produit = a.ProductName,
                    Type = GetAlertTypeText(a.Type),
                    Severite = a.Severity.ToString(),
                    Message = a.Message,
                    StockActuel = a.CurrentStock,
                    Seuil = a.ThresholdValue,
                    Acquittee = a.IsAcknowledged ? "Oui" : "Non"
                }).ToList();
            }

            // Générer le fichier d'export
            await GenerateExportFile(exportData);

            NotificationService.Success("Export généré avec succès!");
            HideExportModal();
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'export: {ex.Message}");
        }
        finally
        {
            _isExporting = false;
            StateHasChanged();
        }
    }

    private async Task GenerateExportFile(Dictionary<string, object> data)
    {
        try
        {
            if (_exportFormat == "csv")
            {
                await GenerateCSVExport(data);
            }
            else
            {
                await GenerateJSONExport(data);
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Erreur lors de la génération du fichier: {ex.Message}");
        }
    }

    private async Task GenerateCSVExport(Dictionary<string, object> data)
    {
        var csvContent = new StringBuilder();
        var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

        foreach (var kvp in data)
        {
            csvContent.AppendLine($"=== {kvp.Key.ToUpper()} ===");

            if (kvp.Value is IEnumerable<object> items)
            {
                var itemsList = items.ToList();
                if (itemsList.Any())
                {
                    // En-têtes
                    var firstItem = itemsList.First();
                    var properties = firstItem.GetType().GetProperties();
                    csvContent.AppendLine(string.Join(";", properties.Select(p => p.Name)));

                    // Données
                    foreach (var item in itemsList)
                    {
                        var values = properties.Select(p => p.GetValue(item)?.ToString() ?? "").ToArray();
                        csvContent.AppendLine(string.Join(";", values));
                    }
                }
            }
            csvContent.AppendLine();
        }

        var fileName = $"inventaire_export_{timestamp}.csv";
        await DownloadFile(fileName, csvContent.ToString(), "text/csv");
    }

    private async Task GenerateJSONExport(Dictionary<string, object> data)
    {
        var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        var jsonContent = JsonSerializer.Serialize(data, new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        var fileName = $"inventaire_export_{timestamp}.json";
        await DownloadFile(fileName, jsonContent, "application/json");
    }

    private async Task DownloadFile(string fileName, string content, string contentType)
    {
        var bytes = Encoding.UTF8.GetBytes(content);
        var base64 = Convert.ToBase64String(bytes);

        await JSRuntime.InvokeVoidAsync("downloadFile", fileName, base64, contentType);
    }

    private async Task ExportMovements()
    {
        try
        {
            var csvContent = new StringBuilder();
            csvContent.AppendLine("Date;Produit;Type;Quantite;Stock_Avant;Stock_Apres;Raison;Utilisateur;Reference;Notes");

            foreach (var movement in _filteredMovements)
            {
                csvContent.AppendLine($"{movement.CreatedAt:dd/MM/yyyy HH:mm};{movement.ProductName};{GetMovementTypeText(movement.Type)};{movement.Quantity};{movement.PreviousStock};{movement.NewStock};{movement.Reason};{movement.UserName};{movement.Reference};{movement.Notes}");
            }

            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var fileName = $"mouvements_stock_{timestamp}.csv";

            await DownloadFile(fileName, csvContent.ToString(), "text/csv");
            NotificationService.Success("Export des mouvements généré avec succès!");
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'export: {ex.Message}");
        }
    }
}
