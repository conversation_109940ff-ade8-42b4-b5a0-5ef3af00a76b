# Script PowerShell pour insérer les données de test dans NafaPlace
# Executer ce script depuis le repertoire racine du projet

Write-Host "Insertion des donnees de test pour NafaPlace..." -ForegroundColor Green

# Configuration des bases de donnees
$identityDb = "nafaplace_identity"
$catalogDb = "nafaplace_catalog"
$inventoryDb = "nafaplace_inventory"
$host = "localhost"
$port = "5432"
$username = "postgres"
$password = "postgres"

# Fonction pour executer une commande SQL
function Execute-SQL {
    param(
        [string]$Database,
        [string]$Query
    )

    $env:PGPASSWORD = $password
    $result = psql -h $host -p $port -U $username -d $Database -c $Query
    return $result
}

# Fonction pour executer un fichier SQL
function Execute-SQLFile {
    param(
        [string]$Database,
        [string]$FilePath
    )

    $env:PGPASSWORD = $password
    $result = psql -h $host -p $port -U $username -d $Database -f $FilePath
    return $result
}

try {
    Write-Host "Verification de la connexion aux bases de donnees..." -ForegroundColor Yellow

    # Verifier la connexion a chaque base de donnees
    $databases = @($identityDb, $catalogDb, $inventoryDb)
    foreach ($db in $databases) {
        Write-Host "  Connexion a $db..." -ForegroundColor Cyan
        $result = Execute-SQL -Database $db -Query "SELECT 1;"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  Connexion reussie a $db" -ForegroundColor Green
        } else {
            Write-Host "  Echec de connexion a $db" -ForegroundColor Red
            throw "Impossible de se connecter a la base de donnees $db"
        }
    }
    
    Write-Host "`n🗃️ Insertion des données de test..." -ForegroundColor Yellow
    
    # 1. Insérer les utilisateurs dans Identity
    Write-Host "  Insertion des utilisateurs..." -ForegroundColor Cyan
    $userQueries = @(
        "INSERT INTO ""Users"" (""Email"", ""Username"", ""PasswordHash"", ""PhoneNumber"", ""EmailConfirmed"", ""PhoneNumberConfirmed"", ""TwoFactorEnabled"", ""IsActive"", ""FirstName"", ""LastName"", ""Roles"", ""CreatedAt"", ""UpdatedAt"") VALUES ('<EMAIL>', 'admin', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000001', true, true, false, true, 'Admin', 'NafaPlace', 'Admin', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;",
        "INSERT INTO ""Users"" (""Email"", ""Username"", ""PasswordHash"", ""PhoneNumber"", ""EmailConfirmed"", ""PhoneNumberConfirmed"", ""TwoFactorEnabled"", ""IsActive"", ""FirstName"", ""LastName"", ""Roles"", ""CreatedAt"", ""UpdatedAt"") VALUES ('<EMAIL>', 'seller1', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000002', true, true, false, true, 'Mamadou', 'Diallo', 'Seller', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;",
        "INSERT INTO ""Users"" (""Email"", ""Username"", ""PasswordHash"", ""PhoneNumber"", ""EmailConfirmed"", ""PhoneNumberConfirmed"", ""TwoFactorEnabled"", ""IsActive"", ""FirstName"", ""LastName"", ""Roles"", ""CreatedAt"", ""UpdatedAt"") VALUES ('<EMAIL>', 'seller2', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000003', true, true, false, true, 'Fatoumata', 'Camara', 'Seller', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;",
        "INSERT INTO ""Users"" (""Email"", ""Username"", ""PasswordHash"", ""PhoneNumber"", ""EmailConfirmed"", ""PhoneNumberConfirmed"", ""TwoFactorEnabled"", ""IsActive"", ""FirstName"", ""LastName"", ""Roles"", ""CreatedAt"", ""UpdatedAt"") VALUES ('<EMAIL>', 'seller3', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000004', true, true, false, true, 'Ibrahima', 'Bah', 'Seller', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;"
    )
    
    foreach ($query in $userQueries) {
        Execute-SQL -Database $identityDb -Query $query | Out-Null
    }
    
    # Insérer les relations utilisateur-rôle
    $roleQueries = @(
        "INSERT INTO ""UserRoles"" (""UserId"", ""RoleId"", ""CreatedAt"") SELECT u.""Id"", 1, NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' ON CONFLICT DO NOTHING;",
        "INSERT INTO ""UserRoles"" (""UserId"", ""RoleId"", ""CreatedAt"") SELECT u.""Id"", 3, NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' ON CONFLICT DO NOTHING;",
        "INSERT INTO ""UserRoles"" (""UserId"", ""RoleId"", ""CreatedAt"") SELECT u.""Id"", 3, NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' ON CONFLICT DO NOTHING;",
        "INSERT INTO ""UserRoles"" (""UserId"", ""RoleId"", ""CreatedAt"") SELECT u.""Id"", 3, NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' ON CONFLICT DO NOTHING;"
    )
    
    foreach ($query in $roleQueries) {
        Execute-SQL -Database $identityDb -Query $query | Out-Null
    }
    
    Write-Host "  ✅ Utilisateurs insérés avec succès" -ForegroundColor Green
    
    # 2. Insérer les catégories dans Catalog
    Write-Host "  Insertion des catégories..." -ForegroundColor Cyan
    $categoryQueries = @(
        "INSERT INTO ""Categories"" (""Name"", ""Description"", ""ImageUrl"", ""IconUrl"", ""IsActive"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Électronique', 'Appareils électroniques et gadgets', 'https://via.placeholder.com/300x200?text=Electronique', 'fas fa-laptop', true, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;",
        "INSERT INTO ""Categories"" (""Name"", ""Description"", ""ImageUrl"", ""IconUrl"", ""IsActive"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Téléphones', 'Smartphones et accessoires', 'https://via.placeholder.com/300x200?text=Telephones', 'fas fa-mobile-alt', true, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;",
        "INSERT INTO ""Categories"" (""Name"", ""Description"", ""ImageUrl"", ""IconUrl"", ""IsActive"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Mode', 'Vêtements et accessoires de mode', 'https://via.placeholder.com/300x200?text=Mode', 'fas fa-tshirt', true, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"
    )
    
    foreach ($query in $categoryQueries) {
        Execute-SQL -Database $catalogDb -Query $query | Out-Null
    }
    
    Write-Host "  ✅ Catégories insérées avec succès" -ForegroundColor Green
    
    # 3. Insérer les vendeurs dans Catalog
    Write-Host "  Insertion des vendeurs..." -ForegroundColor Cyan
    $sellerQueries = @(
        "INSERT INTO ""Sellers"" (""Name"", ""Email"", ""PhoneNumber"", ""Address"", ""IsActive"", ""UserId"", ""CreatedAt"", ""UpdatedAt"") SELECT 'TechStore Guinea', '<EMAIL>', '+224620000002', 'Kaloum, Conakry', true, u.""Id""::text, NOW(), NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' AND NOT EXISTS (SELECT 1 FROM ""Sellers"" s WHERE s.""Email"" = '<EMAIL>');",
        "INSERT INTO ""Sellers"" (""Name"", ""Email"", ""PhoneNumber"", ""Address"", ""IsActive"", ""UserId"", ""CreatedAt"", ""UpdatedAt"") SELECT 'Fashion Conakry', '<EMAIL>', '+224620000003', 'Matam, Conakry', true, u.""Id""::text, NOW(), NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' AND NOT EXISTS (SELECT 1 FROM ""Sellers"" s WHERE s.""Email"" = '<EMAIL>');",
        "INSERT INTO ""Sellers"" (""Name"", ""Email"", ""PhoneNumber"", ""Address"", ""IsActive"", ""UserId"", ""CreatedAt"", ""UpdatedAt"") SELECT 'ElectroMax', '<EMAIL>', '+224620000004', 'Ratoma, Conakry', true, u.""Id""::text, NOW(), NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' AND NOT EXISTS (SELECT 1 FROM ""Sellers"" s WHERE s.""Email"" = '<EMAIL>');"
    )
    
    foreach ($query in $sellerQueries) {
        Execute-SQL -Database $catalogDb -Query $query | Out-Null
    }
    
    Write-Host "  ✅ Vendeurs insérés avec succès" -ForegroundColor Green
    
    Write-Host "`n📈 Affichage du résumé des données..." -ForegroundColor Yellow
    
    # Afficher le résumé
    Write-Host "  📊 Résumé des données insérées:" -ForegroundColor Cyan
    
    $userCount = Execute-SQL -Database $identityDb -Query "SELECT COUNT(*) FROM ""Users"";"
    Write-Host "    - Utilisateurs: $($userCount.Trim())" -ForegroundColor White
    
    $categoryCount = Execute-SQL -Database $catalogDb -Query "SELECT COUNT(*) FROM ""Categories"";"
    Write-Host "    - Catégories: $($categoryCount.Trim())" -ForegroundColor White
    
    $sellerCount = Execute-SQL -Database $catalogDb -Query "SELECT COUNT(*) FROM ""Sellers"";"
    Write-Host "    - Vendeurs: $($sellerCount.Trim())" -ForegroundColor White
    
    Write-Host "`n🎉 Données de test insérées avec succès!" -ForegroundColor Green
    Write-Host "📝 Informations de connexion:" -ForegroundColor Yellow
    Write-Host "  - Admin: <EMAIL> / Test123!" -ForegroundColor White
    Write-Host "  - Vendeur 1: <EMAIL> / Test123!" -ForegroundColor White
    Write-Host "  - Vendeur 2: <EMAIL> / Test123!" -ForegroundColor White
    Write-Host "  - Vendeur 3: <EMAIL> / Test123!" -ForegroundColor White
    
} catch {
    Write-Host "❌ Erreur lors de l'insertion des données: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ Script terminé avec succès!" -ForegroundColor Green
