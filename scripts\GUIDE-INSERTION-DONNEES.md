# 📋 Guide d'Insertion des Données de Test - NafaPlace

## 🎯 Objectif
Insérer des données de test dans les bases de données NafaPlace pour tester la fonctionnalité de mise à jour en lot des stocks.

## 🔧 Prérequis
- ✅ Services Docker en cours d'exécution
- ✅ pgAdmin accessible sur http://localhost:5050
- ✅ Accès aux bases de données PostgreSQL

## 📊 Données à Insérer

### 👥 Utilisateurs
- **Admin**: <EMAIL> / Test123!
- **Vendeur 1**: <EMAIL> / Test123! (Mamadou Diallo - TechStore Guinea)
- **Vendeur 2**: <EMAIL> / Test123! (Fatoumata Camara - Fashion Conakry)
- **Vendeur 3**: <EMAIL> / Test123! (<PERSON><PERSON> - ElectroMax)

### 📦 Produits (21 produits avec différents niveaux de stock)
- **Stock normal** (>10): iPad Air (12), AirPods Pro (25), <PERSON><PERSON><PERSON> (15), etc.
- **Stock faible** (≤10): iPhone 15 Pro (5), MacBook Pro M3 (3), Casque Gaming (7), etc.
- **Rupture de stock** (0): Samsung Galaxy S24, Drone 4K, Montre Connectée

## 🚀 Instructions Étape par Étape

### Étape 1: Accéder à pgAdmin
1. Ouvrir le navigateur et aller à http://localhost:5050
2. Se connecter avec:
   - **Email**: <EMAIL>
   - **Mot de passe**: admin

### Étape 2: Configurer les Connexions aux Bases de Données
1. Clic droit sur "Servers" → "Register" → "Server"
2. **Onglet General**:
   - Name: NafaPlace Databases
3. **Onglet Connection**:
   - Host: nafaplace-catalog-db (ou localhost)
   - Port: 5432
   - Username: postgres
   - Password: postgres
4. Cliquer "Save"

### Étape 3: Insérer les Données Identity
1. Naviguer vers: Servers → NafaPlace Databases → Databases → nafaplace_identity
2. Clic droit sur nafaplace_identity → "Query Tool"
3. Copier-coller la section "DONNÉES POUR IDENTITY DATABASE" du fichier `pgadmin-insert-data.sql`
4. Cliquer sur "Execute" (F5)
5. Vérifier que les utilisateurs sont créés

### Étape 4: Insérer les Données Catalog
1. Naviguer vers: nafaplace_catalog
2. Ouvrir "Query Tool"
3. Copier-coller la section "DONNÉES POUR CATALOG DATABASE" du fichier `pgadmin-insert-data.sql`
4. Cliquer sur "Execute" (F5)
5. Vérifier les catégories, vendeurs et produits

### Étape 5: Insérer les Données Inventory
1. Naviguer vers: nafaplace_inventory
2. Ouvrir "Query Tool"
3. Copier-coller la section "DONNÉES POUR INVENTORY DATABASE" du fichier `pgadmin-insert-data.sql`
4. Cliquer sur "Execute" (F5)
5. Vérifier les mouvements de stock et réservations

## ✅ Vérification des Données

### Vérifier les Utilisateurs (nafaplace_identity)
```sql
SELECT "Id", "Email", "Username", "FirstName", "LastName", "Roles", "IsActive" 
FROM "Users" 
ORDER BY "Id";
```

### Vérifier les Produits par Stock (nafaplace_catalog)
```sql
SELECT 
    s."Name" as "Vendeur",
    p."Name" as "Produit", 
    p."StockQuantity" as "Stock",
    CASE 
        WHEN p."StockQuantity" = 0 THEN 'Rupture'
        WHEN p."StockQuantity" <= 10 THEN 'Stock faible'
        ELSE 'Stock normal'
    END as "Statut Stock"
FROM "Products" p
JOIN "Sellers" s ON p."SellerId" = s."Id"
ORDER BY s."Name", p."StockQuantity";
```

### Vérifier les Alertes de Stock (nafaplace_inventory)
```sql
SELECT "ProductName", "Type", "Severity", "CurrentStock", "IsActive"
FROM "StockAlerts"
WHERE "IsActive" = true;
```

## 🧪 Test de la Fonctionnalité

### Étape 1: Connexion au Portail Vendeur
1. Aller à http://localhost:8082
2. Se <NAME_EMAIL> / Test123!

### Étape 2: Accéder à l'Inventaire
1. Cliquer sur "Inventaire" dans le menu
2. Vérifier que le dashboard affiche les bonnes statistiques

### Étape 3: Tester la Mise à Jour en Lot
1. Cliquer sur "Mise à Jour en Lot"
2. Vérifier que les produits s'affichent correctement
3. Tester les filtres:
   - Stock faible (≤10)
   - Rupture de stock
   - Stock normal (>10)
4. Tester la sélection multiple
5. Tester les actions rapides (0, 10, 50 stock)
6. Effectuer une mise à jour en lot

## 📊 Résumé des Données Attendues

| Base de Données | Table | Nombre d'Enregistrements |
|----------------|-------|--------------------------|
| nafaplace_identity | Users | 5 |
| nafaplace_identity | UserRoles | 5 |
| nafaplace_catalog | Categories | 5 |
| nafaplace_catalog | Sellers | 3 |
| nafaplace_catalog | Products | 21 |
| nafaplace_catalog | ProductImages | 21 |
| nafaplace_inventory | StockMovements | 5 |
| nafaplace_inventory | StockReservations | 3 |
| nafaplace_inventory | StockAlerts | 2 (seed data) |

## 🔍 Répartition des Produits par Stock

- **Stock normal (>10)**: 8 produits
- **Stock faible (1-10)**: 11 produits
- **Rupture de stock (0)**: 3 produits

## 🎯 Scénarios de Test Recommandés

1. **Test de filtrage**: Vérifier que les filtres fonctionnent correctement
2. **Test de sélection**: Sélectionner différents produits
3. **Test d'actions rapides**: Appliquer 0, 10, 50 stock
4. **Test de validation**: Essayer de soumettre sans sélection
5. **Test de mise à jour**: Effectuer une vraie mise à jour
6. **Test de pagination**: Si plus de 20 produits
7. **Test de recherche**: Rechercher par nom de produit

## 🚨 Dépannage

### Problème: Connexion refusée à la base de données
- Vérifier que les conteneurs Docker sont en cours d'exécution
- Utiliser `localhost` au lieu du nom du conteneur si nécessaire

### Problème: Erreur de contrainte lors de l'insertion
- Vérifier que les données de référence (catégories, vendeurs) sont insérées en premier
- Utiliser `ON CONFLICT DO NOTHING` pour éviter les doublons

### Problème: Produits non visibles dans le portail
- Vérifier que `ApprovalStatus = 1` (approuvé)
- Vérifier que `IsActive = true`
- Vérifier que le vendeur est associé au bon utilisateur

## ✅ Validation Finale

Une fois toutes les données insérées, vous devriez pouvoir:
1. ✅ Se connecter au portail vendeur avec les comptes de test
2. ✅ Voir les produits dans la page Inventaire
3. ✅ Utiliser tous les filtres de la mise à jour en lot
4. ✅ Effectuer des mises à jour de stock en lot
5. ✅ Voir les mouvements de stock dans l'historique
