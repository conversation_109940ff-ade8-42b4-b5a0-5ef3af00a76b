# Script simple pour inserer les donnees de test
Write-Host "Insertion des donnees de test..." -ForegroundColor Green

# Configuration
$env:PGPASSWORD = "postgres"
$host = "localhost"
$port = "5432"
$username = "postgres"

# 1. Inserer les utilisateurs dans Identity
Write-Host "Insertion des utilisateurs..." -ForegroundColor Yellow
psql -h $host -p $port -U $username -d nafaplace_identity -c "INSERT INTO ""Users"" (""Email"", ""Username"", ""PasswordHash"", ""PhoneNumber"", ""EmailConfirmed"", ""PhoneNumberConfirmed"", ""TwoFactorEnabled"", ""IsActive"", ""FirstName"", ""LastName"", ""Roles"", ""CreatedAt"", ""UpdatedAt"") VALUES ('<EMAIL>', 'admin', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000001', true, true, false, true, 'Admin', 'NafaPlace', 'Admin', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_identity -c "INSERT INTO ""Users"" (""Email"", ""Username"", ""PasswordHash"", ""PhoneNumber"", ""EmailConfirmed"", ""PhoneNumberConfirmed"", ""TwoFactorEnabled"", ""IsActive"", ""FirstName"", ""LastName"", ""Roles"", ""CreatedAt"", ""UpdatedAt"") VALUES ('<EMAIL>', 'seller1', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000002', true, true, false, true, 'Mamadou', 'Diallo', 'Seller', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_identity -c "INSERT INTO ""Users"" (""Email"", ""Username"", ""PasswordHash"", ""PhoneNumber"", ""EmailConfirmed"", ""PhoneNumberConfirmed"", ""TwoFactorEnabled"", ""IsActive"", ""FirstName"", ""LastName"", ""Roles"", ""CreatedAt"", ""UpdatedAt"") VALUES ('<EMAIL>', 'seller2', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000003', true, true, false, true, 'Fatoumata', 'Camara', 'Seller', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;"

# Inserer les relations utilisateur-role
psql -h $host -p $port -U $username -d nafaplace_identity -c "INSERT INTO ""UserRoles"" (""UserId"", ""RoleId"", ""CreatedAt"") SELECT u.""Id"", 1, NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' ON CONFLICT DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_identity -c "INSERT INTO ""UserRoles"" (""UserId"", ""RoleId"", ""CreatedAt"") SELECT u.""Id"", 3, NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' ON CONFLICT DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_identity -c "INSERT INTO ""UserRoles"" (""UserId"", ""RoleId"", ""CreatedAt"") SELECT u.""Id"", 3, NOW() FROM ""Users"" u WHERE u.""Email"" = '<EMAIL>' ON CONFLICT DO NOTHING;"

# 2. Inserer les categories dans Catalog
Write-Host "Insertion des categories..." -ForegroundColor Yellow
psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Categories"" (""Name"", ""Description"", ""ImageUrl"", ""IconUrl"", ""IsActive"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Electronique', 'Appareils electroniques et gadgets', 'https://via.placeholder.com/300x200?text=Electronique', 'fas fa-laptop', true, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Categories"" (""Name"", ""Description"", ""ImageUrl"", ""IconUrl"", ""IsActive"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Telephones', 'Smartphones et accessoires', 'https://via.placeholder.com/300x200?text=Telephones', 'fas fa-mobile-alt', true, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Categories"" (""Name"", ""Description"", ""ImageUrl"", ""IconUrl"", ""IsActive"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Mode', 'Vetements et accessoires de mode', 'https://via.placeholder.com/300x200?text=Mode', 'fas fa-tshirt', true, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

# 3. Inserer les vendeurs dans Catalog
Write-Host "Insertion des vendeurs..." -ForegroundColor Yellow
psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Sellers"" (""Name"", ""Email"", ""PhoneNumber"", ""Address"", ""IsActive"", ""UserId"", ""CreatedAt"", ""UpdatedAt"") VALUES ('TechStore Guinea', '<EMAIL>', '+224620000002', 'Kaloum, Conakry', true, '2', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Sellers"" (""Name"", ""Email"", ""PhoneNumber"", ""Address"", ""IsActive"", ""UserId"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Fashion Conakry', '<EMAIL>', '+224620000003', 'Matam, Conakry', true, '3', NOW(), NOW()) ON CONFLICT (""Email"") DO NOTHING;"

# 4. Inserer quelques produits de test
Write-Host "Insertion des produits..." -ForegroundColor Yellow
psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('iPhone 15 Pro', 'Dernier iPhone avec puce A17 Pro', 1200000.00, 2, 5, 'GNF', 'Apple', 0.19, 4.8, true, true, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Samsung Galaxy S24', 'Smartphone Android haut de gamme', 950000.00, 2, 0, 'GNF', 'Samsung', 0.17, 4.7, true, true, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('MacBook Pro M3', 'Ordinateur portable professionnel', 2500000.00, 1, 3, 'GNF', 'Apple', 1.4, 4.9, true, true, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('iPad Air', 'Tablette polyvalente pour travail et loisirs', 800000.00, 1, 12, 'GNF', 'Apple', 0.46, 4.6, true, false, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('AirPods Pro', 'Ecouteurs sans fil avec reduction de bruit', 350000.00, 1, 25, 'GNF', 'Apple', 0.05, 4.5, true, true, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Robe Africaine Elegante', 'Robe traditionnelle guineenne moderne', 150000.00, 3, 8, 'GNF', 'Local', 0.3, 4.4, true, true, 2, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Casque Gaming', 'Casque gamer avec micro', 85000.00, 1, 7, 'GNF', 'Razer', 0.3, 4.3, true, false, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Souris Gaming', 'Souris optique haute precision', 45000.00, 1, 9, 'GNF', 'Logitech', 0.12, 4.4, true, false, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Drone 4K', 'Drone avec camera 4K stabilisee', 450000.00, 1, 0, 'GNF', 'DJI', 0.25, 4.7, true, true, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

psql -h $host -p $port -U $username -d nafaplace_catalog -c "INSERT INTO ""Products"" (""Name"", ""Description"", ""Price"", ""CategoryId"", ""StockQuantity"", ""Currency"", ""Brand"", ""Weight"", ""Rating"", ""IsActive"", ""IsFeatured"", ""SellerId"", ""ApprovalStatus"", ""CreatedAt"", ""UpdatedAt"") VALUES ('Montre Connectee', 'Smartwatch avec GPS et cardio', 280000.00, 1, 0, 'GNF', 'Apple', 0.04, 4.8, true, true, 1, 1, NOW(), NOW()) ON CONFLICT (""Name"") DO NOTHING;"

# Afficher le resume
Write-Host "Resume des donnees inserees:" -ForegroundColor Green
psql -h $host -p $port -U $username -d nafaplace_identity -c "SELECT 'Utilisateurs:' as info, COUNT(*) as count FROM ""Users"";"
psql -h $host -p $port -U $username -d nafaplace_catalog -c "SELECT 'Categories:' as info, COUNT(*) as count FROM ""Categories"";"
psql -h $host -p $port -U $username -d nafaplace_catalog -c "SELECT 'Vendeurs:' as info, COUNT(*) as count FROM ""Sellers"";"
psql -h $host -p $port -U $username -d nafaplace_catalog -c "SELECT 'Produits:' as info, COUNT(*) as count FROM ""Products"";"

Write-Host "Donnees inserees avec succes!" -ForegroundColor Green
Write-Host "Informations de connexion:" -ForegroundColor Yellow
Write-Host "  - Admin: <EMAIL> / Test123!" -ForegroundColor White
Write-Host "  - Vendeur 1: <EMAIL> / Test123!" -ForegroundColor White
Write-Host "  - Vendeur 2: <EMAIL> / Test123!" -ForegroundColor White
