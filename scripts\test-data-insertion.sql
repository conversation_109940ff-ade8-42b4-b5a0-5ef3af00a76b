-- Script d'insertion de données de test pour NafaPlace
-- Exécuter ce script dans pgAdmin ou via psql

-- =====================================================
-- 1. DONNÉES DE TEST POUR IDENTITY DATABASE
-- =====================================================

-- Connexion à la base de données Identity
\c nafaplace_identity;

-- Insérer des utilisateurs de test avec des mots de passe hashés
-- Mot de passe pour tous: "Test123!"
-- Hash généré avec BCrypt pour "Test123!"
INSERT INTO "Users" ("Email", "Username", "PasswordHash", "PhoneNumber", "EmailConfirmed", "PhoneNumberConfirmed", "TwoFactorEnabled", "IsActive", "FirstName", "LastName", "Roles", "CreatedAt", "UpdatedAt")
VALUES 
    ('<EMAIL>', 'admin', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000001', true, true, false, true, 'Admin', 'NafaPlace', 'Admin', NOW(), NOW()),
    ('<EMAIL>', 'seller1', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000002', true, true, false, true, 'Mamadou', 'Diallo', 'Seller', NOW(), NOW()),
    ('<EMAIL>', 'seller2', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000003', true, true, false, true, 'Fatoumata', 'Camara', 'Seller', NOW(), NOW()),
    ('<EMAIL>', 'seller3', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000004', true, true, false, true, 'Ibrahima', 'Bah', 'Seller', NOW(), NOW()),
    ('<EMAIL>', 'user1', '$2a$11$8K1p/a0dBxeH4F2aGAGOdOehHbvVdqvM4f8KjvJ2.nU5s/RuBm/Gy', '+224620000005', true, true, false, true, 'Aminata', 'Touré', 'User', NOW(), NOW());

-- Insérer les relations utilisateur-rôle
INSERT INTO "UserRoles" ("UserId", "RoleId", "CreatedAt")
VALUES 
    (1, 1, NOW()), -- Admin -> Admin role
    (2, 3, NOW()), -- Seller1 -> Seller role
    (3, 3, NOW()), -- Seller2 -> Seller role
    (4, 3, NOW()), -- Seller3 -> Seller role
    (5, 2, NOW()); -- User1 -> User role

-- =====================================================
-- 2. DONNÉES DE TEST POUR CATALOG DATABASE
-- =====================================================

-- Connexion à la base de données Catalog
\c nafaplace_catalog;

-- Insérer des catégories de test
INSERT INTO "Categories" ("Name", "Description", "ImageUrl", "IconUrl", "IsActive", "CreatedAt", "UpdatedAt")
VALUES 
    ('Électronique', 'Appareils électroniques et gadgets', 'https://via.placeholder.com/300x200?text=Electronique', 'fas fa-laptop', true, NOW(), NOW()),
    ('Téléphones', 'Smartphones et accessoires', 'https://via.placeholder.com/300x200?text=Telephones', 'fas fa-mobile-alt', true, NOW(), NOW()),
    ('Ordinateurs', 'Ordinateurs portables et de bureau', 'https://via.placeholder.com/300x200?text=Ordinateurs', 'fas fa-desktop', true, NOW(), NOW()),
    ('Mode', 'Vêtements et accessoires de mode', 'https://via.placeholder.com/300x200?text=Mode', 'fas fa-tshirt', true, NOW(), NOW()),
    ('Maison', 'Articles pour la maison et décoration', 'https://via.placeholder.com/300x200?text=Maison', 'fas fa-home', true, NOW(), NOW());

-- Insérer des vendeurs de test
INSERT INTO "Sellers" ("Name", "Email", "PhoneNumber", "Address", "IsActive", "UserId", "CreatedAt", "UpdatedAt")
VALUES 
    ('TechStore Guinea', '<EMAIL>', '+224620000002', 'Kaloum, Conakry', true, '2', NOW(), NOW()),
    ('Fashion Conakry', '<EMAIL>', '+224620000003', 'Matam, Conakry', true, '3', NOW(), NOW()),
    ('ElectroMax', '<EMAIL>', '+224620000004', 'Ratoma, Conakry', true, '4', NOW(), NOW());

-- Insérer des produits de test avec différents niveaux de stock
INSERT INTO "Products" ("Name", "Description", "Price", "CategoryId", "StockQuantity", "Currency", "Brand", "Model", "Weight", "Rating", "IsActive", "IsFeatured", "SellerId", "ApprovalStatus", "CreatedAt", "UpdatedAt")
VALUES 
    -- Produits TechStore Guinea (SellerId = 1)
    ('iPhone 15 Pro', 'Dernier iPhone avec puce A17 Pro', 1200000.00, 2, 5, 'GNF', 'Apple', 'iPhone 15 Pro', 0.19, 4.8, true, true, 1, 1, NOW(), NOW()),
    ('Samsung Galaxy S24', 'Smartphone Android haut de gamme', 950000.00, 2, 0, 'GNF', 'Samsung', 'Galaxy S24', 0.17, 4.7, true, true, 1, 1, NOW(), NOW()),
    ('MacBook Pro M3', 'Ordinateur portable professionnel', 2500000.00, 3, 3, 'GNF', 'Apple', 'MacBook Pro', 1.4, 4.9, true, true, 1, 1, NOW(), NOW()),
    ('iPad Air', 'Tablette polyvalente pour travail et loisirs', 800000.00, 1, 12, 'GNF', 'Apple', 'iPad Air', 0.46, 4.6, true, false, 1, 1, NOW(), NOW()),
    ('AirPods Pro', 'Écouteurs sans fil avec réduction de bruit', 350000.00, 1, 25, 'GNF', 'Apple', 'AirPods Pro', 0.05, 4.5, true, true, 1, 1, NOW(), NOW()),
    
    -- Produits Fashion Conakry (SellerId = 2)
    ('Robe Africaine Élégante', 'Robe traditionnelle guinéenne moderne', 150000.00, 4, 8, 'GNF', 'Local', 'Traditionnel', 0.3, 4.4, true, true, 2, 1, NOW(), NOW()),
    ('Chemise Homme Business', 'Chemise professionnelle en coton', 75000.00, 4, 15, 'GNF', 'Fashion', 'Business', 0.2, 4.2, true, false, 2, 1, NOW(), NOW()),
    ('Sac à Main Cuir', 'Sac à main en cuir véritable', 200000.00, 4, 6, 'GNF', 'Leather Co', 'Premium', 0.8, 4.7, true, true, 2, 1, NOW(), NOW()),
    ('Chaussures Sport', 'Baskets confortables pour le sport', 120000.00, 4, 20, 'GNF', 'SportMax', 'Runner', 0.4, 4.3, true, false, 2, 1, NOW(), NOW()),
    
    -- Produits ElectroMax (SellerId = 3)
    ('Télévision 55" 4K', 'Smart TV 4K avec Android TV', 1800000.00, 1, 2, 'GNF', 'Samsung', '55AU7000', 15.5, 4.6, true, true, 3, 1, NOW(), NOW()),
    ('Réfrigérateur 300L', 'Réfrigérateur économique', 1200000.00, 5, 4, 'GNF', 'LG', 'GR-B300', 45.0, 4.4, true, false, 3, 1, NOW(), NOW()),
    ('Micro-ondes', 'Four micro-ondes 25L', 180000.00, 5, 10, 'GNF', 'Panasonic', 'NN-ST25', 12.0, 4.2, true, false, 3, 1, NOW(), NOW()),
    ('Climatiseur Split', 'Climatiseur 12000 BTU', 850000.00, 5, 1, 'GNF', 'Midea', 'MSA12', 35.0, 4.5, true, true, 3, 1, NOW(), NOW()),
    ('Ventilateur Plafond', 'Ventilateur avec télécommande', 95000.00, 5, 30, 'GNF', 'Orient', 'Ceiling Fan', 3.5, 4.1, true, false, 3, 1, NOW(), NOW()),
    
    -- Produits avec stock faible pour tester les filtres
    ('Casque Gaming', 'Casque gamer avec micro', 85000.00, 1, 7, 'GNF', 'Razer', 'BlackShark', 0.3, 4.3, true, false, 1, 1, NOW(), NOW()),
    ('Souris Gaming', 'Souris optique haute précision', 45000.00, 1, 9, 'GNF', 'Logitech', 'G502', 0.12, 4.4, true, false, 1, 1, NOW(), NOW()),
    ('Clavier Mécanique', 'Clavier gaming mécanique RGB', 125000.00, 1, 4, 'GNF', 'Corsair', 'K70', 1.2, 4.6, true, false, 1, 1, NOW(), NOW()),
    ('Webcam HD', 'Caméra web 1080p pour streaming', 65000.00, 1, 6, 'GNF', 'Logitech', 'C920', 0.16, 4.2, true, false, 1, 1, NOW(), NOW()),
    ('Enceinte Bluetooth', 'Haut-parleur portable étanche', 55000.00, 1, 18, 'GNF', 'JBL', 'Flip 6', 0.55, 4.5, true, false, 1, 1, NOW(), NOW()),
    
    -- Produits en rupture de stock
    ('Drone 4K', 'Drone avec caméra 4K stabilisée', 450000.00, 1, 0, 'GNF', 'DJI', 'Mini 3', 0.25, 4.7, true, true, 1, 1, NOW(), NOW()),
    ('Montre Connectée', 'Smartwatch avec GPS et cardio', 280000.00, 1, 0, 'GNF', 'Apple', 'Watch Series 9', 0.04, 4.8, true, true, 1, 1, NOW(), NOW());

-- Insérer des images de produits
INSERT INTO "ProductImages" ("ProductId", "Url", "IsPrimary", "CreatedAt")
VALUES 
    (1, 'https://via.placeholder.com/400x400?text=iPhone+15+Pro', true, NOW()),
    (2, 'https://via.placeholder.com/400x400?text=Galaxy+S24', true, NOW()),
    (3, 'https://via.placeholder.com/400x400?text=MacBook+Pro', true, NOW()),
    (4, 'https://via.placeholder.com/400x400?text=iPad+Air', true, NOW()),
    (5, 'https://via.placeholder.com/400x400?text=AirPods+Pro', true, NOW()),
    (6, 'https://via.placeholder.com/400x400?text=Robe+Africaine', true, NOW()),
    (7, 'https://via.placeholder.com/400x400?text=Chemise+Homme', true, NOW()),
    (8, 'https://via.placeholder.com/400x400?text=Sac+Cuir', true, NOW()),
    (9, 'https://via.placeholder.com/400x400?text=Chaussures+Sport', true, NOW()),
    (10, 'https://via.placeholder.com/400x400?text=TV+55+4K', true, NOW());

-- =====================================================
-- 3. DONNÉES DE TEST POUR INVENTORY DATABASE
-- =====================================================

-- Connexion à la base de données Inventory
\c nafaplace_inventory;

-- Les alertes de stock sont déjà créées via le seed data dans le DbContext
-- Insérer des mouvements de stock pour l'historique
INSERT INTO "StockMovements" ("ProductId", "ProductName", "Type", "Quantity", "PreviousStock", "NewStock", "Reason", "Reference", "UserId", "UserName", "SellerId", "Notes", "CreatedAt")
VALUES 
    (1, 'iPhone 15 Pro', 0, 10, 15, 5, 'Vente en magasin', 'SALE-001', '2', 'Mamadou Diallo', 1, 'Vente de 10 unités', NOW() - INTERVAL '2 days'),
    (2, 'Samsung Galaxy S24', 0, 5, 5, 0, 'Commande en ligne', 'ORDER-002', '2', 'Mamadou Diallo', 1, 'Rupture de stock', NOW() - INTERVAL '1 day'),
    (3, 'MacBook Pro M3', 1, 5, 8, 3, 'Retour défectueux', 'RETURN-003', '2', 'Mamadou Diallo', 1, 'Retour de 5 unités défectueuses', NOW() - INTERVAL '3 hours'),
    (10, 'Télévision 55" 4K', 0, 3, 5, 2, 'Vente promotionnelle', 'PROMO-004', '4', 'Ibrahima Bah', 3, 'Promotion weekend', NOW() - INTERVAL '1 day'),
    (13, 'Climatiseur Split', 0, 1, 2, 1, 'Installation client', 'INSTALL-005', '4', 'Ibrahima Bah', 3, 'Installation chez client', NOW() - INTERVAL '6 hours');

-- Insérer des réservations de stock actives
INSERT INTO "StockReservations" ("ProductId", "UserId", "SessionId", "Quantity", "Status", "ReservedAt", "ExpiresAt", "Reason")
VALUES 
    (4, '5', 'session-001', 2, 0, NOW() - INTERVAL '10 minutes', NOW() + INTERVAL '20 minutes', 'Panier en cours'),
    (15, '5', 'session-002', 1, 0, NOW() - INTERVAL '5 minutes', NOW() + INTERVAL '25 minutes', 'Commande en préparation'),
    (9, '5', 'session-003', 3, 0, NOW() - INTERVAL '15 minutes', NOW() + INTERVAL '15 minutes', 'Réservation temporaire');

-- Afficher un résumé des données insérées
SELECT 'RÉSUMÉ DES DONNÉES INSÉRÉES:' as info;
SELECT 'Identity - Utilisateurs créés:' as info, COUNT(*) as count FROM "Users";
SELECT 'Catalog - Catégories créées:' as info, COUNT(*) as count FROM "Categories";
SELECT 'Catalog - Vendeurs créés:' as info, COUNT(*) as count FROM "Sellers";
SELECT 'Catalog - Produits créés:' as info, COUNT(*) as count FROM "Products";
SELECT 'Inventory - Mouvements créés:' as info, COUNT(*) as count FROM "StockMovements";
SELECT 'Inventory - Réservations créées:' as info, COUNT(*) as count FROM "StockReservations";
