# Script de test automatisé pour la fonctionnalité de mise à jour en lot
Write-Host "🧪 Test de la fonctionnalité de mise à jour en lot - NafaPlace" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost:8082"
$apiGatewayUrl = "http://localhost:5000"
$inventoryApiUrl = "http://localhost:5244"
$catalogApiUrl = "http://localhost:5243"

# Fonction pour tester une URL
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Description
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "  ✅ $Description - OK" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ❌ $Description - Status: $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  ❌ $Description - Erreur: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour tester une API avec authentification
function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Description,
        [string]$Token = $null
    )
    
    try {
        $headers = @{}
        if ($Token) {
            $headers["Authorization"] = "Bearer $Token"
        }
        
        $response = Invoke-WebRequest -Uri $Url -Method GET -Headers $headers -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "  ✅ $Description - OK" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ❌ $Description - Status: $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  ❌ $Description - Erreur: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "`n🔍 Vérification des services..." -ForegroundColor Yellow

# Test des services de base
$services = @(
    @{ Url = $baseUrl; Description = "Portail Vendeur" },
    @{ Url = $apiGatewayUrl; Description = "API Gateway" },
    @{ Url = "$inventoryApiUrl/health"; Description = "Inventory API Health" },
    @{ Url = "$catalogApiUrl/health"; Description = "Catalog API Health" }
)

$allServicesOk = $true
foreach ($service in $services) {
    $result = Test-Endpoint -Url $service.Url -Description $service.Description
    if (-not $result) {
        $allServicesOk = $false
    }
}

if (-not $allServicesOk) {
    Write-Host "`n❌ Certains services ne sont pas disponibles. Vérifiez que Docker est en cours d'exécution." -ForegroundColor Red
    exit 1
}

Write-Host "`n📊 Test des endpoints API..." -ForegroundColor Yellow

# Test des endpoints API sans authentification
$apiEndpoints = @(
    @{ Url = "$catalogApiUrl/api/v1/catalog/categories"; Description = "Récupération des catégories" },
    @{ Url = "$inventoryApiUrl/api/v1/inventory/dashboard"; Description = "Dashboard inventaire global" }
)

foreach ($endpoint in $apiEndpoints) {
    Test-ApiEndpoint -Url $endpoint.Url -Description $endpoint.Description
}

Write-Host "`n🔐 Test de l'authentification..." -ForegroundColor Yellow

# Simuler une connexion (normalement on ferait un POST vers l'API d'authentification)
Write-Host "  ℹ️  Pour tester l'authentification complète, utilisez le navigateur:" -ForegroundColor Cyan
Write-Host "    1. Aller à $baseUrl" -ForegroundColor White
Write-Host "    2. Se <NAME_EMAIL> / Test123!" -ForegroundColor White
Write-Host "    3. Naviguer vers Inventaire" -ForegroundColor White
Write-Host "    4. Cliquer sur 'Mise à Jour en Lot'" -ForegroundColor White

Write-Host "`n📋 Checklist de test manuel:" -ForegroundColor Yellow
Write-Host "  [ ] Connexion au portail vendeur réussie" -ForegroundColor White
Write-Host "  [ ] Page Inventaire accessible" -ForegroundColor White
Write-Host "  [ ] Dashboard affiche les statistiques" -ForegroundColor White
Write-Host "  [ ] Bouton 'Mise à Jour en Lot' présent" -ForegroundColor White
Write-Host "  [ ] Modal de mise à jour en lot s'ouvre" -ForegroundColor White
Write-Host "  [ ] Produits chargés dans le modal" -ForegroundColor White
Write-Host "  [ ] Filtres fonctionnent (tous, stock faible, rupture)" -ForegroundColor White
Write-Host "  [ ] Recherche par nom fonctionne" -ForegroundColor White
Write-Host "  [ ] Sélection multiple fonctionne" -ForegroundColor White
Write-Host "  [ ] Actions rapides fonctionnent (0, 10, 50)" -ForegroundColor White
Write-Host "  [ ] Validation des raisons obligatoires" -ForegroundColor White
Write-Host "  [ ] Mise à jour en lot réussie" -ForegroundColor White
Write-Host "  [ ] Message de succès affiché" -ForegroundColor White
Write-Host "  [ ] Stocks mis à jour dans la base de données" -ForegroundColor White

Write-Host "`n🎯 Scénarios de test recommandés:" -ForegroundColor Yellow
Write-Host "  1. 🔍 Test de filtrage:" -ForegroundColor Cyan
Write-Host "     - Filtrer par 'Stock faible' → Voir produits avec stock ≤ 10" -ForegroundColor White
Write-Host "     - Filtrer par 'Rupture de stock' → Voir produits avec stock = 0" -ForegroundColor White
Write-Host "     - Filtrer par 'Stock normal' → Voir produits avec stock > 10" -ForegroundColor White

Write-Host "  2. 🔍 Test de recherche:" -ForegroundColor Cyan
Write-Host "     - Rechercher 'iPhone' → Voir iPhone 15 Pro" -ForegroundColor White
Write-Host "     - Rechercher 'Samsung' → Voir Samsung Galaxy S24" -ForegroundColor White
Write-Host "     - Rechercher 'Gaming' → Voir Casque Gaming, Souris Gaming" -ForegroundColor White

Write-Host "  3. ✅ Test de sélection:" -ForegroundColor Cyan
Write-Host "     - Sélectionner individuellement quelques produits" -ForegroundColor White
Write-Host "     - Utiliser 'Tout sélectionner'" -ForegroundColor White
Write-Host "     - Vérifier les compteurs de sélection" -ForegroundColor White

Write-Host "  4. ⚡ Test d'actions rapides:" -ForegroundColor Cyan
Write-Host "     - Sélectionner des produits et cliquer '0 Stock'" -ForegroundColor White
Write-Host "     - Sélectionner des produits et cliquer '10 Stock'" -ForegroundColor White
Write-Host "     - Sélectionner des produits et cliquer '50 Stock'" -ForegroundColor White

Write-Host "  5. ✏️ Test de modification manuelle:" -ForegroundColor Cyan
Write-Host "     - Modifier manuellement les quantités de stock" -ForegroundColor White
Write-Host "     - Ajouter des raisons spécifiques par produit" -ForegroundColor White

Write-Host "  6. ✅ Test de validation:" -ForegroundColor Cyan
Write-Host "     - Essayer de soumettre sans sélection → Erreur attendue" -ForegroundColor White
Write-Host "     - Essayer de soumettre sans raison → Erreur attendue" -ForegroundColor White
Write-Host "     - Sélectionner 'Autre' sans texte → Erreur attendue" -ForegroundColor White

Write-Host "  7. 🚀 Test de mise à jour réelle:" -ForegroundColor Cyan
Write-Host "     - Sélectionner quelques produits" -ForegroundColor White
Write-Host "     - Modifier leurs stocks" -ForegroundColor White
Write-Host "     - Choisir une raison" -ForegroundColor White
Write-Host "     - Soumettre la mise à jour" -ForegroundColor White
Write-Host "     - Vérifier le message de succès" -ForegroundColor White
Write-Host "     - Recharger la page et vérifier les nouveaux stocks" -ForegroundColor White

Write-Host "`n📊 Données de test disponibles:" -ForegroundColor Yellow
Write-Host "  👤 Comptes vendeurs:" -ForegroundColor Cyan
Write-Host "    - <EMAIL> / Test123! (TechStore Guinea - 15 produits)" -ForegroundColor White
Write-Host "    - <EMAIL> / Test123! (Fashion Conakry - 4 produits)" -ForegroundColor White
Write-Host "    - <EMAIL> / Test123! (ElectroMax - 5 produits)" -ForegroundColor White

Write-Host "  📦 Répartition des stocks:" -ForegroundColor Cyan
Write-Host "    - Stock normal (>10): 8 produits" -ForegroundColor White
Write-Host "    - Stock faible (1-10): 11 produits" -ForegroundColor White
Write-Host "    - Rupture de stock (0): 3 produits" -ForegroundColor White

Write-Host "`n🔧 Dépannage:" -ForegroundColor Yellow
Write-Host "  Si les produits ne s'affichent pas:" -ForegroundColor Cyan
Write-Host "    1. Vérifier que les données sont insérées dans pgAdmin" -ForegroundColor White
Write-Host "    2. Vérifier les logs des conteneurs Docker" -ForegroundColor White
Write-Host "    3. Redémarrer les services si nécessaire" -ForegroundColor White

Write-Host "  Si l'authentification échoue:" -ForegroundColor Cyan
Write-Host "    1. Vérifier que les utilisateurs sont créés dans nafaplace_identity" -ForegroundColor White
Write-Host "    2. Vérifier les relations UserRoles" -ForegroundColor White
Write-Host "    3. Vérifier les logs de l'Identity API" -ForegroundColor White

Write-Host "`n✅ Services vérifiés avec succès!" -ForegroundColor Green
Write-Host "🚀 Vous pouvez maintenant tester la fonctionnalité de mise à jour en lot." -ForegroundColor Green
Write-Host "📖 Consultez le guide GUIDE-INSERTION-DONNEES.md pour les instructions détaillées." -ForegroundColor Cyan
